R_DEF: Internal format may change without notice
local
anim tp_speed_dialog_enter
anim tp_speed_dialog_exit
anim tp_speed_dropdown_enter
anim tp_speed_dropdown_exit
animator tp_video_button_press
animator tp_video_button_release
array image_format_options
attr? autoPlay
attr? doubleTapEnabled
attr? maxScale
attr? panEnabled
attr? zoomEnabled
color black
color tp_video_button_bg_disabled
color tp_video_button_bg_normal
color tp_video_button_bg_pressed
color tp_video_button_stroke_disabled
color tp_video_button_stroke_normal
color tp_video_button_stroke_pressed
color tp_video_button_text_color
color tp_video_play_button_bg_normal
color tp_video_play_button_bg_pressed
color tp_video_progress_bg
color tp_video_progress_primary
color tp_video_progress_secondary
color tp_video_progress_thumb
color tp_video_progress_thumb_stroke
color tp_video_text_disabled
color tp_video_text_normal
color white
drawable dialog_background
drawable ic_fast_forward_white_24
drawable ic_fast_rewind_white_24
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_pause_white_24
drawable ic_play_arrow_white_24
drawable ic_settings_white_24
drawable ic_skip_next_white_24
drawable ic_skip_previous_white_24
drawable ic_step_frame_white_24
drawable tp_speed_dropdown_background
drawable tp_speed_item_background
drawable tp_speed_item_selected_background
drawable tp_speed_menu_background
drawable tp_video_button_background
drawable tp_video_controls_background
drawable tp_video_play_button_background
drawable tp_video_progress_drawable
drawable tp_video_progress_thumb
drawable tp_video_settings_button_background
id browserButton
id btn_apply_scene
id btn_biological_scene
id btn_browse_media
id btn_browse_path
id btn_cancel
id btn_capture
id btn_clear_cache
id btn_close_network
id btn_close_popup
id btn_close_settings
id btn_delete_scene
id btn_fast_backward
id btn_fast_forward
id btn_forward
id btn_load_scene
id btn_next_video
id btn_open_network
id btn_open_smb
id btn_play_pause
id btn_previous_video
id btn_record
id btn_refresh_paths
id btn_rewind
id btn_save
id btn_save_scene
id btn_save_settings
id btn_settings
id btn_speed
id btn_step_decode
id btn_step_frame
id btn_stereoscopic_scene
id btn_switch_mode
id btn_test_connection
id captureButton
id cb_enable_smb
id cb_enable_smb_video
id checkbox_auto_exposure
id controlPanel
id control_panel
id controls_layout
id default_content
id et_password
id et_server_ip
id et_share_name
id et_username
id ethernet_status_text
id hotspot_button
id hotspot_status_text
id image_view
id input_scene_value
id media_name
id media_thumbnail
id menu_arrow
id menu_horizontal_flip
id menu_icon
id menu_image_format
id menu_network_settings
id menu_roi_mode
id menu_scene_mode
id menu_smb_settings
id menu_title
id menu_tv_mode
id menu_vertical_flip
id network_info_text
id network_interface_spinner
id preview_container
id progress_bar
id progress_loading_paths
id radio_camera_stream
id radio_group_wb
id radio_screen_stream
id radio_wb_auto
id radio_wb_manual
id radio_wb_roi
id rb_format_bmp
id rb_format_jpeg
id rb_format_png
id recordButton
id recycler_view
id resolutionTestButton
id rg_image_format
id roiView
id roi_view
id rtsp_status_text
id rtsp_url_text
id seek_bar
id seekbar_brightness
id seekbar_contrast
id seekbar_dark_enhance
id seekbar_denoise
id seekbar_exposure_compensation
id seekbar_exposure_gain
id seekbar_exposure_time
id seekbar_gamma
id seekbar_hue
id seekbar_progress
id seekbar_saturation
id seekbar_sharpness
id seekbar_wb_blue
id seekbar_wb_green
id seekbar_wb_red
id settingsButton
id settings_content_frame
id settings_menu_recycler
id settings_panel
id speed_0_5
id speed_0_75
id speed_1_0
id speed_1_25
id speed_1_5
id speed_2_0
id spinner_image_format
id spinner_remote_path
id start_rtsp_button
id stop_rtsp_button
id stream_type_radio_group
id switch_horizontal_flip
id switch_roi_mode
id switch_to_camera_button
id switch_to_tv_button
id switch_vertical_flip
id test_button
id textureView
id texture_view
id tp_video_player_view
id tv_brightness_value
id tv_connection_status
id tv_contrast_value
id tv_current_position
id tv_current_time
id tv_dark_enhance_value
id tv_denoise_value
id tv_duration
id tv_exposure_compensation_value
id tv_exposure_gain_value
id tv_exposure_time_value
id tv_gamma_value
id tv_hue_value
id tv_mode_description_text
id tv_mode_status_text
id tv_mode_switch
id tv_saturation_value
id tv_sharpness_value
id tv_total_time
id tv_wb_blue_value
id tv_wb_green_value
id tv_wb_red_value
id tv_zoom_scale
id wifi_button
id wifi_status_text
layout activity_main
layout activity_tp_video_player_new
layout decoder
layout dialog_image_format_settings
layout dialog_smb_settings
layout dialog_tp_settings
layout dialog_tp_test
layout encoder
layout fragment_tp_network_settings
layout fragment_tp_smb_settings
layout fragment_tp_tv_mode_settings
layout image_viewer
layout item_settings_menu
layout media_browser
layout media_browser_integrated
layout media_item
layout network_settings
layout popup_menu
layout spinner_item
layout tp_speed_dropdown_menu
layout tp_speed_selection_dialog
layout tp_video_player_controls
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Base.Theme.MediacodecNew
style FullScreenDialog
style Theme.MediacodecNew
style TpVideoControlButton
style TpVideoPlayButton
style TpVideoProgressBar
style TpVideoSettingsButton
style TpVideoSpeedDialog
style TpVideoSpeedDialogAnimation
style TpVideoSpeedDropdownAnimation
style TpVideoSpeedDropdownItem
style TpVideoSpeedItem
style TpVideoTimeDisplay
style VideoControlButton
style VideoSpeedButton
styleable TpImageView maxScale zoomEnabled panEnabled doubleTapEnabled
styleable TpVideoPlayerView autoPlay
xml backup_rules
xml data_extraction_rules
