<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 测试按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎛️ ISP场景管理"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="12dp"
            android:textColor="?android:attr/textColorPrimary" />

        <!-- 默认场景区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp"
            android:background="?android:attr/selectableItemBackground"
            android:padding="4dp">

            <!-- 默认场景标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔬 默认场景："
                android:textSize="12sp"
                android:textStyle="bold"
                android:gravity="start"
                android:layout_marginBottom="4dp"
                android:textColor="?android:attr/textColorPrimary" />

            <!-- 默认场景按钮行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_biological_scene"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_weight="1"
                    android:text="生物"
                    android:textSize="10sp"
                    android:layout_marginEnd="6dp"
                    android:backgroundTint="@android:color/holo_green_light"
                    android:textColor="@android:color/white"
                    style="?android:attr/buttonStyleSmall" />

                <Button
                    android:id="@+id/btn_stereoscopic_scene"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_weight="1"
                    android:text="体视"
                    android:textSize="10sp"
                    android:layout_marginStart="6dp"
                    android:backgroundTint="@android:color/holo_blue_light"
                    android:textColor="@android:color/white"
                    style="?android:attr/buttonStyleSmall" />

            </LinearLayout>

        </LinearLayout>

        <!-- 场景管理按钮区域 - 3个核心按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_save_scene"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="6dp"
                android:text="保存场景"
                android:textSize="11sp"
                android:padding="6dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_load_scene"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="6dp"
                android:text="加载场景"
                android:textSize="11sp"
                android:padding="6dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_delete_scene"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginStart="6dp"
                android:text="删除场景"
                android:textSize="11sp"
                android:padding="6dp"
                android:backgroundTint="@android:color/holo_orange_light"
                android:textColor="@android:color/white"
                style="?android:attr/buttonStyleSmall" />

        </LinearLayout>

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        android:layout_marginBottom="12dp" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        style="?android:attr/progressBarStyleHorizontal"
        android:indeterminate="true" />

    <!-- ISP参数调节区域 - 紧凑版 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎛️ ISP参数调节"
        android:textSize="12sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="6dp"
        android:textColor="?android:attr/textColorPrimary" />

    <!-- ISP参数调节控件容器 - 静态布局 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="500dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="4dp">

            <!-- 曝光控制组 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📸 曝光控制"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="#2196F3"
                android:padding="8dp" />

            <CheckBox
                android:id="@+id/checkbox_auto_exposure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="自动曝光"
                android:textSize="10sp"
                android:textColor="#333333" />

            <!-- 曝光补偿 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="曝光补偿"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_exposure_compensation_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_exposure_compensation"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 曝光时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="曝光时间"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_exposure_time_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_exposure_time"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 曝光增益 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="曝光增益"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_exposure_gain_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_exposure_gain"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 白平衡控制组 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="⚪ 白平衡控制"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="#2196F3"
                android:padding="8dp" />

            <RadioGroup
                android:id="@+id/radio_group_wb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="8dp">

                <RadioButton
                    android:id="@+id/radio_wb_manual"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Manual"
                    android:textSize="9sp" />

                <RadioButton
                    android:id="@+id/radio_wb_auto"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Auto"
                    android:textSize="9sp" />

                <RadioButton
                    android:id="@+id/radio_wb_roi"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="ROI"
                    android:textSize="9sp" />

            </RadioGroup>

            <!-- 红色增益 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="红色增益"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_wb_red_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_wb_red"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 绿色增益 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="绿色增益"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_wb_green_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_wb_green"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 蓝色增益 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="蓝色增益"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_wb_blue_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_wb_blue"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 图像处理组 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎨 图像处理"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="#2196F3"
                android:padding="8dp" />

            <!-- 亮度 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="亮度"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_brightness_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_brightness"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 对比度 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="对比度"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_contrast_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_contrast"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 饱和度 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="饱和度"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_saturation_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_saturation"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 色调 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="色调"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_hue_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_hue"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- Gamma -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Gamma"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_gamma_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_gamma"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 锐度 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="锐度"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_sharpness_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_sharpness"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 降噪增强组 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔧 降噪增强"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="#2196F3"
                android:padding="8dp" />

            <!-- 降噪 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="降噪"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_denoise_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_denoise"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- 暗部增强 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="暗部增强"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_dark_enhance_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10sp" />

                </LinearLayout>

                <SeekBar
                    android:id="@+id/seekbar_dark_enhance"
                    android:layout_width="match_parent"
                    android:layout_height="40dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
